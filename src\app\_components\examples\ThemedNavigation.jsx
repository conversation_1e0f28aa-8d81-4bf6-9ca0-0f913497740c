/**
 * Example themed navigation component
 * Shows how to use theme-aware colors for navigation elements
 */

export function ThemedNavigation({ items = [], className = '' }) {
  return (
    <nav className={`bg-background border-b border-border ${className}`}>
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex-shrink-0">
            <span className="text-2xl font-bold text-text-primary">
              Logo
            </span>
          </div>
          
          {/* Navigation Links */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              {items.map((item, index) => (
                <a
                  key={index}
                  href={item.href}
                  className="text-text-secondary hover:text-link px-3 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  {item.label}
                </a>
              ))}
            </div>
          </div>
          
          {/* <PERSON><PERSON> */}
          <div className="hidden md:block">
            <button className="bg-link text-text-alternative px-4 py-2 rounded-md text-sm font-medium hover:bg-link-primary transition-colors">
              Contact Us
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
}

// Usage example:
// const navItems = [
//   { label: 'Home', href: '/' },
//   { label: 'About', href: '/about' },
//   { label: 'Services', href: '/services' },
//   { label: 'Contact', href: '/contact' },
// ];
// <ThemedNavigation items={navItems} />
