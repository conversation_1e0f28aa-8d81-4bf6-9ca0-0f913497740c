/**
 * Color System Demo Component
 * Demonstrates all available theme-aware color classes
 */

export default function ColorSystemDemo() {
  return (
    <div className="p-8 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-text mb-2">Color System Demo</h1>
        <p className="text-text-secondary">
          This component demonstrates all available theme-aware color classes
        </p>
      </div>

      {/* Background Colors */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-text">Background Colors</h2>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="bg-background border border-border p-4 rounded-lg">
            <div className="text-sm font-medium text-text">bg-background</div>
            <div className="text-xs text-text-secondary">Main background</div>
          </div>
          <div className="bg-background-primary border border-border p-4 rounded-lg">
            <div className="text-sm font-medium text-text">bg-background-primary</div>
            <div className="text-xs text-text-secondary">Primary variant</div>
          </div>
          <div className="bg-background-secondary border border-border p-4 rounded-lg">
            <div className="text-sm font-medium text-text">bg-background-secondary</div>
            <div className="text-xs text-text-secondary">Secondary variant</div>
          </div>
          <div className="bg-background-tertiary border border-border p-4 rounded-lg">
            <div className="text-sm font-medium text-text">bg-background-tertiary</div>
            <div className="text-xs text-text-secondary">Tertiary variant</div>
          </div>
          <div className="bg-background-alternative border border-border p-4 rounded-lg">
            <div className="text-sm font-medium text-text-alternative">bg-background-alternative</div>
            <div className="text-xs text-text-alternative opacity-75">Alternative (dark)</div>
          </div>
        </div>
      </section>

      {/* Text Colors */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-text">Text Colors</h2>
        <div className="bg-background-primary p-6 rounded-lg border border-border">
          <div className="space-y-2">
            <p className="text-text">text-text - Default text color</p>
            <p className="text-text-primary">text-text-primary - Primary text variant</p>
            <p className="text-text-secondary">text-text-secondary - Secondary text variant</p>
            <div className="bg-background-alternative p-3 rounded">
              <p className="text-text-alternative">text-text-alternative - Alternative text (on dark)</p>
            </div>
          </div>
        </div>
      </section>

      {/* Link Colors */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-text">Link Colors</h2>
        <div className="bg-background-primary p-6 rounded-lg border border-border">
          <div className="space-y-2">
            <p><a href="#" className="text-link hover:text-link-primary">text-link - Default link color</a></p>
            <p><a href="#" className="text-link-primary hover:text-link-secondary">text-link-primary - Primary link variant</a></p>
            <p><a href="#" className="text-link-secondary hover:text-link">text-link-secondary - Secondary link variant</a></p>
            <p><a href="#" className="text-link-alternative hover:text-link">text-link-alternative - Alternative link color</a></p>
          </div>
        </div>
      </section>

      {/* Border Colors */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-text">Border Colors</h2>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="bg-background p-4 border-2 border-border rounded-lg">
            <div className="text-sm font-medium text-text">border-border</div>
            <div className="text-xs text-text-secondary">Default border</div>
          </div>
          <div className="bg-background p-4 border-2 border-border-primary rounded-lg">
            <div className="text-sm font-medium text-text">border-border-primary</div>
            <div className="text-xs text-text-secondary">Primary variant</div>
          </div>
          <div className="bg-background p-4 border-2 border-border-secondary rounded-lg">
            <div className="text-sm font-medium text-text">border-border-secondary</div>
            <div className="text-xs text-text-secondary">Secondary variant</div>
          </div>
          <div className="bg-background p-4 border-2 border-border-tertiary rounded-lg">
            <div className="text-sm font-medium text-text">border-border-tertiary</div>
            <div className="text-xs text-text-secondary">Tertiary variant</div>
          </div>
          <div className="bg-background p-4 border-2 border-border-alternative rounded-lg">
            <div className="text-sm font-medium text-text">border-border-alternative</div>
            <div className="text-xs text-text-secondary">Alternative border</div>
          </div>
        </div>
      </section>

      {/* Component Examples */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-text">Component Examples</h2>
        <div className="space-y-4">
          {/* Card Example */}
          <div className="bg-background-primary border border-border rounded-lg p-6">
            <h3 className="text-lg font-semibold text-text mb-2">Example Card</h3>
            <p className="text-text-secondary mb-4">
              This card uses theme-aware colors that will adapt when themes change.
            </p>
            <div className="flex gap-2">
              <button className="bg-link text-text-alternative px-4 py-2 rounded hover:bg-link-primary transition-colors">
                Primary Button
              </button>
              <button className="bg-background-secondary text-text border border-border px-4 py-2 rounded hover:bg-background-tertiary transition-colors">
                Secondary Button
              </button>
            </div>
          </div>

          {/* Navigation Example */}
          <nav className="bg-background border-b border-border">
            <div className="px-6 py-4 flex gap-6">
              <a href="#" className="text-link hover:text-link-primary transition-colors">Home</a>
              <a href="#" className="text-link hover:text-link-primary transition-colors">About</a>
              <a href="#" className="text-link hover:text-link-primary transition-colors">Services</a>
              <a href="#" className="text-link hover:text-link-primary transition-colors">Contact</a>
            </div>
          </nav>
        </div>
      </section>

      {/* Static Colors Note */}
      <section className="space-y-4">
        <h2 className="text-2xl font-semibold text-text">Static Colors (Never Change)</h2>
        <div className="bg-background-primary p-6 rounded-lg border border-border">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-semibold text-text">System Colors</h3>
              <div className="bg-system-success-green-light border border-system-success-green p-3 rounded">
                <span className="text-system-success-green font-medium">Success State</span>
              </div>
              <div className="bg-system-error-red-light border border-system-error-red p-3 rounded">
                <span className="text-system-error-red font-medium">Error State</span>
              </div>
            </div>
            <div className="space-y-2">
              <h3 className="font-semibold text-text">Brand Colors</h3>
              <div className="bg-brand-black text-brand-white p-3 rounded">
                <span className="font-medium">Brand Black</span>
              </div>
              <div className="bg-brand-white text-brand-black border border-neutral-lighter p-3 rounded">
                <span className="font-medium">Brand White</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
