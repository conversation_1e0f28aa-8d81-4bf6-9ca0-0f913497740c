"use client";

import React, { useState } from 'react';
import { THEMES } from '../utils/theme';
import ColorSystemDemo from '../_components/examples/ColorSystemDemo';
import { ThemedButton } from '../_components/examples/ThemedButton';
import { ThemedCard } from '../_components/examples/ThemedCard';
import { ThemedNavigation } from '../_components/examples/ThemedNavigation';

export default function ThemeDemoPage() {
  const [currentTheme, setCurrentTheme] = useState(THEMES.HOMEPAGE);

  const navItems = [
    { label: 'Home', href: '/' },
    { label: 'About', href: '/about' },
    { label: 'Services', href: '/services' },
    { label: 'Contact', href: '/contact' },
  ];

  return (
    <div className={currentTheme}>
      <div className="min-h-screen bg-background">
        {/* Theme Switcher */}
        <div className="bg-background-primary border-b border-border p-4">
          <div className="container mx-auto">
            <h1 className="text-2xl font-bold text-text mb-4">Theme Demo</h1>
            <div className="flex flex-wrap gap-2">
              <span className="text-text-secondary mr-4">Switch Theme:</span>
              {Object.entries(THEMES).map(([name, themeClass]) => (
                <button
                  key={name}
                  onClick={() => setCurrentTheme(themeClass)}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    currentTheme === themeClass
                      ? 'bg-link text-text-alternative'
                      : 'bg-background-secondary text-text hover:bg-background-tertiary'
                  }`}
                >
                  {name.charAt(0) + name.slice(1).toLowerCase().replace('forward', ' Forward')}
                </button>
              ))}
            </div>
            <p className="text-text-secondary mt-2 text-sm">
              Current theme: <span className="font-mono">{currentTheme}</span>
            </p>
          </div>
        </div>

        {/* Navigation Example */}
        <ThemedNavigation items={navItems} />

        {/* Main Content */}
        <div className="container mx-auto py-8">
          {/* Component Examples */}
          <section className="mb-12">
            <h2 className="text-3xl font-bold text-text mb-6">Component Examples</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
              <ThemedCard 
                title="Default Card" 
                subtitle="This is a default themed card"
                variant="default"
              >
                <p className="text-text-secondary mb-4">
                  This card uses the default background and border colors.
                </p>
                <ThemedButton variant="primary" size="sm">
                  Primary Action
                </ThemedButton>
              </ThemedCard>

              <ThemedCard 
                title="Primary Card" 
                subtitle="This is a primary themed card"
                variant="primary"
              >
                <p className="text-text-secondary mb-4">
                  This card uses the primary background variant.
                </p>
                <ThemedButton variant="secondary" size="sm">
                  Secondary Action
                </ThemedButton>
              </ThemedCard>

              <ThemedCard 
                title="Alternative Card" 
                subtitle="This is an alternative themed card"
                variant="alternative"
              >
                <p className="text-text-alternative mb-4">
                  This card uses the alternative (dark) background.
                </p>
                <ThemedButton variant="outline" size="sm" className="border-text-alternative text-text-alternative hover:bg-text-alternative hover:text-background">
                  Outline Action
                </ThemedButton>
              </ThemedCard>
            </div>

            {/* Button Examples */}
            <div className="bg-background-primary p-6 rounded-lg border border-border mb-8">
              <h3 className="text-xl font-semibold text-text mb-4">Button Variants</h3>
              <div className="flex flex-wrap gap-4">
                <ThemedButton variant="primary">Primary</ThemedButton>
                <ThemedButton variant="secondary">Secondary</ThemedButton>
                <ThemedButton variant="outline">Outline</ThemedButton>
                <ThemedButton variant="ghost">Ghost</ThemedButton>
              </div>
              <div className="mt-4 flex flex-wrap gap-4">
                <ThemedButton variant="primary" size="sm">Small</ThemedButton>
                <ThemedButton variant="primary" size="md">Medium</ThemedButton>
                <ThemedButton variant="primary" size="lg">Large</ThemedButton>
              </div>
            </div>
          </section>

          {/* Color System Demo */}
          <ColorSystemDemo />

          {/* Theme Information */}
          <section className="mt-12">
            <div className="bg-background-primary p-6 rounded-lg border border-border">
              <h2 className="text-2xl font-bold text-text mb-4">Theme Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-semibold text-text mb-2">Current Theme</h3>
                  <p className="text-text-secondary mb-4">
                    You are currently viewing the <span className="font-mono bg-background-secondary px-2 py-1 rounded">{currentTheme}</span> theme.
                  </p>
                  <p className="text-text-secondary">
                    All themes currently use the same color palette. You can easily customize individual themes by editing the CSS files in <code className="bg-background-secondary px-1 rounded">src/app/themes/</code>.
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-text mb-2">Available Themes</h3>
                  <ul className="space-y-1 text-text-secondary">
                    <li><span className="font-mono">theme-homepage</span> - Default homepage theme</li>
                    <li><span className="font-mono">theme-forestforward</span> - Forest Forward section theme</li>
                    <li><span className="font-mono">theme-lagom</span> - Lagom section theme</li>
                    <li><span className="font-mono">theme-storyforward</span> - Story Forward section theme</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
