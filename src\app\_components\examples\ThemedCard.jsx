/**
 * Example themed card component
 * Shows how to use theme-aware colors for backgrounds, borders, and text
 */

export function ThemedCard({ 
  children, 
  title,
  subtitle,
  variant = 'default',
  className = '',
  ...props 
}) {
  const baseClasses = 'rounded-lg shadow-small transition-shadow hover:shadow-medium';
  
  const variantClasses = {
    default: 'bg-background border border-border',
    primary: 'bg-background-primary border border-border-primary',
    secondary: 'bg-background-secondary border border-border-secondary',
    alternative: 'bg-background-alternative border border-border-alternative',
  };
  
  return (
    <div
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
      {...props}
    >
      {(title || subtitle) && (
        <div className="p-6 pb-4">
          {title && (
            <h3 className="text-xl font-semibold text-text-primary mb-2">
              {title}
            </h3>
          )}
          {subtitle && (
            <p className="text-text-secondary">
              {subtitle}
            </p>
          )}
        </div>
      )}
      <div className="p-6 pt-0">
        {children}
      </div>
    </div>
  );
}

// Usage examples:
// <ThemedCard title="Card Title" subtitle="Card subtitle">Content here</ThemedCard>
// <ThemedCard variant="primary">Primary styled card</ThemedCard>
// <ThemedCard variant="alternative">Alternative styled card</ThemedCard>
