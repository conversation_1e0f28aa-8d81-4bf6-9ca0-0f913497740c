"use client";

import React from "react";

export function Layout355() {
  return (
    <section id="relume" className="relative">
      <div className="px-[15%]">
        <div className="relative z-10 container">
          <div className="grid auto-cols-fr grid-cols-1 pb-8 md:grid-cols-[1fr_10rem_1fr] md:pb-0 lg:grid-cols-[1fr_12rem_1fr]">
            <div className="relative">
              <div className="flex flex-col justify-center py-8 md:h-screen md:py-0">
                <div className="w-full max-w-sm">
                  <h1 className="mb-5 text-6xl leading-[1.2] font-semibold text-text-alternative md:mb-6 md:text-5xl lg:text-8xl">
                    Walk the talk zeggen ze. Wij doen het.
                  </h1>
                  <div className="flex flex-row gap-4">
                    <button className="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90">Button 1</button>
                    <button className="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90">Button 2</button>
                  </div>
                </div>
              </div>
              
            </div>
            <div className="static top-0 order-first flex h-[50vh] items-center justify-center md:sticky md:order-none md:h-screen">
              {/* Image placeholder - replace src with your desired image */}
            </div>
            <div className="relative md:pt-[150vh]">
              <div className="flex flex-col justify-center py-8 md:h-screen md:py-0">
                <div className="w-full max-w-sm">
                  <h1 className="mb-5 text-4xl leading-[1.2] font-semibold text-text-alternative md:mb-6 md:text-5xl lg:text-6xl">
                    Samen maken we duurzaamheid tastbaar.
                  </h1>
                  <p className="text-text-alternative md:text-md">
                    Klinkt mooi, maar we dóen het ook. Samen met jullie,
                    bedrijven groot en klein. En dan hebben we het over
                    duurzaamheid in al z’n vormen. Over impact maken. Op alle
                    mogelijke manieren. Dat is de meerwaarde van ons ecosysteem.
                    We rollen de mouwen op om samen lokaal natuur te creëren en
                    jouw ESG-verhaal tastbaar te maken. Om jouw bedrijf een stem
                    te geven, zodat je anderen kan inspireren. En om de
                    betrokkenheid van jouw stakeholders te vergroten door ze
                    slim én leuk te verbinden. Zorgen voor daadkracht. En
                    draagvlak creëren. Inspireren en verbinden. Dat is wat we
                    doen!
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div className="mb-[-100vh]" />
        </div>
      </div>
      <div className="sticky bottom-0 z-0 h-screen w-screen">
        <div className="absolute inset-0 z-10 bg-black/50" />
        <div className="sticky bottom-0 h-screen w-screen overflow-hidden">
          <img
            src="https://images.unsplash.com/photo-1441974231531-c6227db76b6e?q=80&w=2071&auto=format&fit=crop"
            alt="Forest landscape background"
            className="absolute -top-full -right-full -bottom-full -left-full m-auto size-full object-cover"
          />
        </div>
      </div>
    </section>
  );
}
