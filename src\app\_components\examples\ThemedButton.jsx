/**
 * Example themed button component
 * Shows how to use theme-aware colors instead of hardcoded Tailwind colors
 */

export function ThemedButton({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  className = '',
  ...props 
}) {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variantClasses = {
    primary: 'bg-link text-text-alternative hover:bg-link-primary focus:ring-link',
    secondary: 'bg-background-secondary text-text-primary border border-border-primary hover:bg-background-tertiary focus:ring-border-primary',
    outline: 'border border-border-primary text-text-primary hover:bg-background-primary focus:ring-border-primary',
    ghost: 'text-text-primary hover:bg-background-primary focus:ring-border-primary',
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm rounded-md',
    md: 'px-4 py-2 text-base rounded-md',
    lg: 'px-6 py-3 text-lg rounded-lg',
  };
  
  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      {...props}
    >
      {children}
    </button>
  );
}

// Usage examples:
// <ThemedButton variant="primary">Primary Button</ThemedButton>
// <ThemedButton variant="secondary">Secondary Button</ThemedButton>
// <ThemedButton variant="outline">Outline Button</ThemedButton>
